# Cấu hình bộ nhớ và GC
org.gradle.jvmargs=-Xmx16G -XX:MaxMetaspaceSize=4G -XX:+HeapDumpOnOutOfMemoryError -XX:+UseParallelGC -XX:MaxGCPauseMillis=100 -XX:GCTimeRatio=4 -XX:ParallelGCThreads=4 -Dfile.encoding=UTF-8 -Duser.language=en

# Cấu hình Android
android.useAndroidX=true
android.enableJetifier=true
android.defaults.buildfeatures.buildconfig=true
android.nonTransitiveRClass=false
android.nonFinalResIds=false

# Cấu hình Gradle
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=true
org.gradle.workers.max=4

# Cấu hình Java (macOS)
# org.gradle.java.home=/Applications/Android Studio.app/Contents/jbr/Contents/Home

# Hiển thị log chi tiết khi build
org.gradle.logging.level=debug

