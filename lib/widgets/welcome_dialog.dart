import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../l10n/app_localizations.dart';
import '../constants/app_text_styles.dart';
import 'safe_text.dart';

/// Dialog chào mừng người dùng mới với thông tin về tính năng sắp có
/// Có option "không hiện lại" để lưu preference
class WelcomeDialog extends StatefulWidget {
  const WelcomeDialog({super.key});

  /// Hiển thị welcome dialog nếu chưa từng hiển thị
  static Future<void> showIfNeeded(BuildContext context) async {
    final prefs = await SharedPreferences.getInstance();
    final hasShown = prefs.getBool('welcome_dialog_shown') ?? false;
    
    if (!hasShown && context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const WelcomeDialog(),
      );
    }
  }

  @override
  State<WelcomeDialog> createState() => _WelcomeDialogState();
}

class _WelcomeDialogState extends State<WelcomeDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  bool _dontShowAgain = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.7,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handleClose() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('welcome_dialog_shown', true);
    
    if (_dontShowAgain) {
      await prefs.setBool('welcome_dialog_dont_show', true);
    }
    
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final l10n = AppLocalizations.of(context);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(24),
              ),
              elevation: 16,
              child: Container(
                constraints: const BoxConstraints(
                  maxWidth: 400,
                  maxHeight: 600, // Giới hạn chiều cao
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      colorScheme.surface,
                      colorScheme.surface.withOpacity(0.95),
                    ],
                  ),
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(24, 32, 24, 32), // Thêm padding trên/dưới
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                    // Welcome icon với gradient (giảm kích thước)
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [
                            colorScheme.primary,
                            colorScheme.primary.withOpacity(0.7),
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: colorScheme.primary.withOpacity(0.3),
                            blurRadius: 20,
                            offset: const Offset(0, 8),
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.restaurant_menu,
                        size: 50,
                        color: Colors.white,
                      ),
                    ),
                    
                    const SizedBox(height: 16), // Giảm spacing

                    // Welcome title (giảm font size)
                    SafeText(
                      l10n?.welcomeToApp ?? 'Chào mừng đến với CookSpark!',
                      style: AppTextStyles.title3(context).withWeight(FontWeight.bold),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                    ),

                    const SizedBox(height: 12), // Giảm spacing
                    
                    // Upcoming features title
                    SafeText(
                      l10n?.upcomingFeatures ?? 'Tính năng sắp có',
                      style: AppTextStyles.title3(context).withWeight(FontWeight.w600)
                          .withColor(colorScheme.primary),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Features description
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: colorScheme.primary.withOpacity(0.1),
                        ),
                      ),
                      child: SafeText(
                        l10n?.upcomingFeaturesDescription ?? 
                        'Chúng tôi đang phát triển nhiều tính năng thú vị:\n\n• Thực đơn tuần cá nhân hóa\n• Lưu và quản lý công thức\n• Gợi ý món ăn thông minh\n• Chia sẻ với bạn bè\n\nHãy theo dõi để cập nhật sớm nhất!',
                        style: AppTextStyles.body(context).withColor(
                          colorScheme.onSurface.withOpacity(0.8),
                        ),
                        textAlign: TextAlign.left,
                        maxLines: 10,
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Don't show again checkbox
                    Row(
                      children: [
                        Checkbox(
                          value: _dontShowAgain,
                          onChanged: (value) {
                            setState(() {
                              _dontShowAgain = value ?? false;
                            });
                          },
                        ),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              setState(() {
                                _dontShowAgain = !_dontShowAgain;
                              });
                            },
                            child: SafeText(
                              l10n?.dontShowAgain ?? 'Không hiện lại',
                              style: AppTextStyles.body(context),
                              maxLines: 1,
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Action button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _handleClose,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: SafeText(
                          l10n?.gotIt ?? 'Đã hiểu',
                          style: AppTextStyles.callout(context)
                              .withWeight(FontWeight.w600)
                              .withColor(Colors.white),
                          maxLines: 1,
                        ),
                      ),
                    ),
                  ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
