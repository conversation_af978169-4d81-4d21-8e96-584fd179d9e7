import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/theme_provider.dart';
import '../l10n/app_localizations.dart';
import '../widgets/safe_text.dart';

// Import các màn hình
import 'home_content_screen.dart';
import 'meal_suggestion_screen.dart';

import 'saved_videos_screen.dart';
import 'settings_screen.dart';

/// Màn hình navigation chính với bottom navigation bar hiện đại
class MainNavigationScreen extends StatefulWidget {
  static const String routeName = '/main-navigation';

  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> {
  int _currentIndex = 0;

  // Danh sách các màn hình
  late final List<Widget> _screens;

  void _onTabChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  void initState() {
    super.initState();
    _screens = [
      HomeContentScreen(onTabChanged: _onTabChanged),
      const MealSuggestionScreen(),
      const SavedVideosScreen(),
      const SettingsScreen(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.darkMode;
    final l10n = AppLocalizations.of(context);

    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _screens,
      ),
      bottomNavigationBar: _buildModernBottomNavBar(context, isDarkMode, l10n),
    );
  }

  Widget _buildModernBottomNavBar(BuildContext context, bool isDarkMode, AppLocalizations? l10n) {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode 
            ? Theme.of(context).colorScheme.surfaceContainerHigh
            : Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: isDarkMode 
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          if (!isDarkMode)
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 40,
              offset: const Offset(0, 16),
              spreadRadius: 0,
            ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: Theme.of(context).primaryColor,
          unselectedItemColor: isDarkMode 
              ? Colors.grey[400] 
              : Colors.grey[600],
          selectedFontSize: 12,
          unselectedFontSize: 10,
          iconSize: 24,
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            height: 1.2,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w400,
            height: 1.2,
          ),
          items: [
            BottomNavigationBarItem(
              icon: _buildNavIcon(Icons.home_outlined, Icons.home, 0),
              label: l10n?.home ?? 'Trang chủ',
            ),
            BottomNavigationBarItem(
              icon: _buildNavIcon(Icons.auto_awesome_outlined, Icons.auto_awesome, 1),
              label: l10n?.mealSuggestion ?? 'Gợi ý',
            ),
            BottomNavigationBarItem(
              icon: _buildNavIcon(Icons.bookmark_outline, Icons.bookmark, 2),
              label: l10n?.savedDishes ?? 'Đã lưu',
            ),
            BottomNavigationBarItem(
              icon: _buildNavIcon(Icons.settings_outlined, Icons.settings, 3),
              label: l10n?.settings ?? 'Cài đặt',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavIcon(IconData outlinedIcon, IconData filledIcon, int index) {
    final isSelected = _currentIndex == index;
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.darkMode;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected 
            ? Theme.of(context).primaryColor.withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Icon(
        isSelected ? filledIcon : outlinedIcon,
        size: 24,
        color: isSelected 
            ? Theme.of(context).primaryColor
            : (isDarkMode ? Colors.grey[400] : Colors.grey[600]),
      ),
    );
  }
}
