import 'package:flutter/material.dart';
import '../services/supabase_auth_service.dart';
import '../widgets/otp_input_widget.dart';
import 'main_navigation_screen.dart';
import 'register_screen.dart';

class OtpVerificationScreen extends StatefulWidget {
  static const routeName = '/otp-verification';
  
  final String email;
  
  const OtpVerificationScreen({
    super.key,
    required this.email,
  });

  @override
  State<OtpVerificationScreen> createState() => _OtpVerificationScreenState();
}

class _OtpVerificationScreenState extends State<OtpVerificationScreen> {
  final _otpController = TextEditingController();
  final _authService = SupabaseAuthService();
  
  bool _isLoading = false;
  bool _isResending = false;
  String? _errorMessage;
  int _resendCountdown = 0;
  
  @override
  void dispose() {
    _otpController.dispose();
    super.dispose();
  }

  /// Xác minh OTP và hoàn tất đăng ký
  /// Ngày tạo: 2024-12-19
  Future<void> _verifyOtp() async {
    // Validate OTP format
    final validation = _authService.validateOtpInput(widget.email, _otpController.text.trim());
    
    if (!validation['isValid']) {
      setState(() {
        _errorMessage = validation['message'];
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      print('=== BẮT ĐẦU XÁC MINH OTP TỪ UI ===');
      print('Email: ${widget.email}');
      print('OTP: ${_otpController.text.trim()}');

      final result = await _authService.verifyOtpAndSignUp(
        widget.email,
        _otpController.text.trim(),
      );

      print('Kết quả xác minh OTP từ Auth Service: $result');

      if (!mounted) {
        print('Widget không còn mounted, dừng xử lý');
        return;
      }

      if (result['success'] == true) {
        print('UI: Xác minh OTP thành công, chuẩn bị chuyển trang');

        // Hiển thị thông báo thành công
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message'] ?? 'Đăng ký thành công!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Đợi thông báo hiển thị xong trước khi chuyển màn hình
        await Future.delayed(Duration(milliseconds: 2200));

        if (!mounted) {
          print('Widget không còn mounted sau thông báo, dừng chuyển trang');
          return;
        }

        print('UI: Chuyển đến trang chính');
        // Đăng ký thành công, chuyển đến màn hình chính
        Navigator.of(context).pushNamedAndRemoveUntil(
          MainNavigationScreen.routeName,
          (route) => false,
        );
      } else {
        print('UI: Xác minh OTP thất bại, hiển thị thông báo lỗi');
        setState(() {
          _errorMessage = result['message'] ?? 'Đã xảy ra lỗi không xác định';
        });

        // Hiển thị thông báo lỗi
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('UI: Lỗi ngoại lệ khi xác minh OTP: $e');
      
      if (!mounted) return;
      
      setState(() {
        _errorMessage = 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
      print('=== KẾT THÚC XÁC MINH OTP TỪ UI ===');
    }
  }

  /// Gửi lại mã OTP
  /// Ngày tạo: 2024-12-19
  Future<void> _resendOtp() async {
    if (_resendCountdown > 0) return;

    setState(() {
      _isResending = true;
      _errorMessage = null;
    });

    try {
      print('=== BẮT ĐẦU GỬI LẠI OTP TỪ UI ===');
      print('Email: ${widget.email}');

      final result = await _authService.resendOtp(widget.email);

      print('Kết quả gửi lại OTP từ Auth Service: $result');

      if (!mounted) {
        print('Widget không còn mounted, dừng xử lý');
        return;
      }

      if (result['success'] == true) {
        print('UI: Gửi lại OTP thành công');

        // Hiển thị thông báo thành công
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result['message'] ?? 'Đã gửi lại mã OTP'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Bắt đầu countdown 60 giây
        _startResendCountdown();
      } else {
        print('UI: Gửi lại OTP thất bại');
        setState(() {
          _errorMessage = result['message'] ?? 'Không thể gửi lại mã OTP';
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage!),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('UI: Lỗi ngoại lệ khi gửi lại OTP: $e');
      
      if (!mounted) return;
      
      setState(() {
        _errorMessage = 'Đã xảy ra lỗi không xác định. Vui lòng thử lại sau.';
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_errorMessage!),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
      print('=== KẾT THÚC GỬI LẠI OTP TỪ UI ===');
    }
  }

  /// Bắt đầu countdown cho nút gửi lại
  /// Ngày tạo: 2024-12-19
  void _startResendCountdown() {
    setState(() {
      _resendCountdown = 60;
    });

    // Countdown timer
    Future.doWhile(() async {
      await Future.delayed(Duration(seconds: 1));
      if (!mounted) return false;
      
      setState(() {
        _resendCountdown--;
      });
      
      return _resendCountdown > 0;
    });
  }

  /// Quay lại màn hình đăng ký
  /// Ngày tạo: 2024-12-19
  void _goBackToRegister() {
    Navigator.of(context).pushReplacementNamed(RegisterScreen.routeName);
  }

  /// Lấy thông báo lỗi cho OTP input
  String? _getOtpErrorText() {
    final otpText = _otpController.text.trim();
    
    if (otpText.isNotEmpty && otpText.length < 6) {
      return 'Mã OTP phải có 6 chữ số';
    }
    
    // Kiểm tra lỗi từ server response
    if (_errorMessage != null && _errorMessage!.toLowerCase().contains('otp')) {
      return _errorMessage;
    }
    
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Stack(
        children: [
          // Gradient background
          Container(
            height: double.infinity,
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  theme.colorScheme.primary,
                  theme.colorScheme.primaryContainer,
                ],
              ),
            ),
          ),

          // Back button
          Positioned(
            top: MediaQuery.of(context).padding.top + 8,
            left: 8,
            child: IconButton(
              icon: Icon(Icons.arrow_back, color: Colors.white),
              onPressed: _goBackToRegister,
            ),
          ),

          // Content
          SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                children: [
                  // Header section
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Icon
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(50),
                          ),
                          child: Icon(
                            Icons.mail_lock_outlined,
                            size: 50,
                            color: Colors.white,
                          ),
                        ),
                        SizedBox(height: 24),
                        
                        Text(
                          'Xác minh mã OTP',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 12),
                        
                        Text(
                          'Chúng tôi đã gửi mã xác minh 6 chữ số đến',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withOpacity(0.9),
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 4),
                        
                        Text(
                          widget.email,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  // OTP Input Section
                  Expanded(
                    flex: 3,
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(24),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Nhập mã xác minh',
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 24),

                          // OTP Input Widget
                          OtpInputWidget(
                            controller: _otpController,
                            enabled: !_isLoading,
                            errorText: _getOtpErrorText(),
                            onChanged: (value) {
                              // Reset error message khi người dùng nhập
                              if (_errorMessage != null) {
                                setState(() {
                                  _errorMessage = null;
                                });
                              }
                            },
                            onCompleted: (otp) {
                              // Auto proceed khi nhập đủ 6 số
                              if (otp.length == 6) {
                                _verifyOtp();
                              }
                            },
                          ),
                          
                          SizedBox(height: 32),

                          // Verify Button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _isLoading ? null : _verifyOtp,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: theme.colorScheme.primary,
                                foregroundColor: Colors.white,
                                padding: EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                elevation: 2,
                              ),
                              child: _isLoading
                                  ? SizedBox(
                                      height: 20,
                                      width: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white,
                                        ),
                                      ),
                                    )
                                  : Text(
                                      'XÁC MINH',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 1,
                                      ),
                                    ),
                            ),
                          ),

                          SizedBox(height: 24),

                          // Resend section
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'Không nhận được mã? ',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                                ),
                              ),
                              TextButton(
                                onPressed: (_isResending || _resendCountdown > 0) 
                                    ? null 
                                    : _resendOtp,
                                child: _isResending
                                    ? SizedBox(
                                        height: 16,
                                        width: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(
                                            theme.colorScheme.primary,
                                          ),
                                        ),
                                      )
                                    : Text(
                                        _resendCountdown > 0
                                            ? 'Gửi lại (${_resendCountdown}s)'
                                            : 'Gửi lại',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                          color: (_resendCountdown > 0)
                                              ? theme.colorScheme.onSurface.withOpacity(0.5)
                                              : theme.colorScheme.primary,
                                        ),
                                      ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: 24),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
