import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../providers/theme_provider.dart';
import '../providers/user_profile_provider.dart';
import '../l10n/app_localizations.dart';
import '../widgets/safe_text.dart';

import '../constants/app_text_styles.dart';

import '../models/user_profile.dart';

/// Nội dung chính của home screen không có AppBar và Drawer
class HomeContentScreen extends StatefulWidget {
  final Function(int)? onTabChanged;

  const HomeContentScreen({
    super.key,
    this.onTabChanged,
  });

  @override
  State<HomeContentScreen> createState() => _HomeContentScreenState();
}

class _HomeContentScreenState extends State<HomeContentScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.darkMode;

    return Scaffold(
      backgroundColor: isDarkMode 
          ? Theme.of(context).colorScheme.surface
          : const Color(0xFFF8F9FA),
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            // Header với avatar và greeting
            SliverToBoxAdapter(
              child: _buildHeader(context, isDarkMode),
            ),
            
            // Main content
            SliverToBoxAdapter(
              child: AnimationLimiter(
                child: Column(
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 600),
                    childAnimationBuilder: (widget) => SlideAnimation(
                      verticalOffset: 50.0,
                      child: FadeInAnimation(child: widget),
                    ),
                    children: [
                      const SizedBox(height: 24),
                      _buildMainActionCard(context, isDarkMode),
                      const SizedBox(height: 24),
                      _buildQuickActions(context, isDarkMode),
                      const SizedBox(height: 100), // Space for bottom nav
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 16, 24, 0),
      child: Row(
        children: [
          // Avatar
          GestureDetector(
            onTap: () {
              // Chuyển đến tab Cài đặt (index 3)
              if (widget.onTabChanged != null) {
                widget.onTabChanged!(3);
              }
            },
            child: FutureBuilder<String?>(
              future: _getAvatarUrl(),
              builder: (context, snapshot) {
                final avatarUrl = snapshot.data;
                return Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).primaryColor.withValues(alpha: 0.15),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: 24,
                    backgroundColor: isDarkMode
                        ? Theme.of(context).colorScheme.surfaceContainerHighest
                        : Theme.of(context).primaryColor.withValues(alpha: 0.08),
                    backgroundImage: avatarUrl != null && avatarUrl.isNotEmpty
                        ? NetworkImage(avatarUrl)
                        : null,
                    child: avatarUrl == null || avatarUrl.isEmpty
                        ? Icon(
                            Icons.person_outline,
                            color: Theme.of(context).primaryColor,
                            size: 24,
                          )
                        : null,
                  ),
                );
              },
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Greeting và title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: SlideTransition(
                    position: _slideAnimation,
                    child: _buildGreetingText(context, isDarkMode),
                  ),
                ),
                const SizedBox(height: 4),
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: SafeText(
                    'CookSpark AI',
                    style: AppTextStyles.titleLarge(context).copyWith(
                      color: isDarkMode
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).primaryColor,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGreetingText(BuildContext context, bool isDarkMode) {
    return Consumer<UserProfileProvider>(
      builder: (context, userProfileProvider, child) {
        final userProfile = userProfileProvider.userProfile;
        return FutureBuilder<String>(
          future: _buildGreeting(userProfile),
          builder: (context, snapshot) {
            final greeting = snapshot.data ?? 'Chào bạn!';
            return SafeText(
              greeting,
              style: AppTextStyles.bodyLarge(context).copyWith(
                color: isDarkMode 
                    ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8)
                    : Colors.grey[600],
                fontWeight: FontWeight.w400,
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildMainActionCard(BuildContext context, bool isDarkMode) {
    final l10n = AppLocalizations.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SafeText(
            l10n?.mealSuggestion ?? 'Gợi ý món ăn hôm nay',
            style: AppTextStyles.headlineSmall(context).copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          SafeText(
            'Khám phá những món ăn ngon phù hợp với sở thích của bạn',
            style: AppTextStyles.bodyMedium(context).copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                // Chuyển đến tab Gợi ý món ăn (index 1)
                if (widget.onTabChanged != null) {
                  widget.onTabChanged!(1);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Theme.of(context).primaryColor,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: SafeText(
                'Bắt đầu gợi ý',
                style: AppTextStyles.labelLarge(context).copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, bool isDarkMode) {
    final l10n = AppLocalizations.of(context);
    final actions = [
      {
        'title': l10n?.savedDishes ?? 'Món ăn đã lưu',
        'subtitle': l10n?.savedVideos ?? 'Video đã lưu',
        'icon': Icons.bookmark,
        'route': 'saved_videos',
      },

      {
        'title': l10n?.settings ?? 'Cài đặt',
        'subtitle': l10n?.customizeApp ?? 'Tùy chỉnh ứng dụng',
        'icon': Icons.settings,
        'route': 'settings',
      },
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SafeText(
            'Truy cập nhanh',
            style: AppTextStyles.titleMedium(context).copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),
          ...actions.map((action) => _buildQuickActionItem(
            context,
            isDarkMode,
            action['title'] as String,
            action['subtitle'] as String,
            action['icon'] as IconData,
            action['route'] as String,
          )),
        ],
      ),
    );
  }

  Widget _buildQuickActionItem(
    BuildContext context,
    bool isDarkMode,
    String title,
    String subtitle,
    IconData icon,
    String route,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: isDarkMode 
            ? Theme.of(context).colorScheme.surfaceContainerHigh
            : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode 
                ? Colors.black.withValues(alpha: 0.1)
                : Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Xử lý navigation dựa trên route
            if (route == 'saved_videos') {
              // Chuyển đến tab Video đã lưu (index 2)
              if (widget.onTabChanged != null) {
                widget.onTabChanged!(2);
              }
            } else if (route == 'settings') {
              // Chuyển đến tab Cài đặt (index 3)
              if (widget.onTabChanged != null) {
                widget.onTabChanged!(3);
              }
            }
            // Bỏ qua weekly_menu vì đã ẩn
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SafeText(
                        title,
                        style: AppTextStyles.titleSmall(context).copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      SafeText(
                        subtitle,
                        style: AppTextStyles.bodySmall(context).copyWith(
                          color: isDarkMode 
                              ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)
                              : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: isDarkMode 
                      ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5)
                      : Colors.grey[400],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<String?> _getAvatarUrl() async {
    try {
      // Thử lấy từ SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final savedPhotoUrl = prefs.getString('photo_url');
      if (savedPhotoUrl != null && savedPhotoUrl.isNotEmpty) {
        return savedPhotoUrl;
      }

      // Thử lấy từ Supabase auth metadata
      final user = Supabase.instance.client.auth.currentUser;
      final authAvatarUrl = user?.userMetadata?['avatar_url'];
      if (authAvatarUrl != null && authAvatarUrl.isNotEmpty) {
        return authAvatarUrl as String;
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  Future<String> _buildGreeting(UserProfile? userProfile) async {
    try {
      final currentUser = Supabase.instance.client.auth.currentUser;
      final now = DateTime.now();

      // Xác định greeting theo thời gian
      String greeting;
      if (now.hour < 12) {
        greeting = 'Chào buổi sáng';
      } else if (now.hour < 18) {
        greeting = 'Chào buổi chiều';
      } else {
        greeting = 'Chào buổi tối';
      }

      // Lấy tên hiển thị
      String displayName = 'bạn';

      // 1. Ưu tiên UserProfile.displayName
      if (userProfile?.displayName != null && userProfile!.displayName!.isNotEmpty) {
        final nameParts = userProfile.displayName!.trim().split(' ');
        displayName = nameParts.isNotEmpty ? nameParts.last : 'bạn';
      }
      // 2. Thử lấy từ auth metadata
      else if (currentUser?.userMetadata?['full_name'] != null) {
        final fullName = currentUser!.userMetadata!['full_name'] as String;
        final nameParts = fullName.trim().split(' ');
        displayName = nameParts.isNotEmpty ? nameParts.last : 'bạn';
      }
      // 3. Thử lấy từ SharedPreferences
      else {
        try {
          final prefs = await SharedPreferences.getInstance();
          final savedDisplayName = prefs.getString('display_name');
          if (savedDisplayName != null && savedDisplayName.isNotEmpty) {
            final nameParts = savedDisplayName.trim().split(' ');
            displayName = nameParts.isNotEmpty ? nameParts.last : 'bạn';
          }
        } catch (e) {
          // Ignore error, use fallback
        }
      }

      return '$greeting, $displayName!';
    } catch (e) {
      return 'Chào bạn!';
    }
  }
}
